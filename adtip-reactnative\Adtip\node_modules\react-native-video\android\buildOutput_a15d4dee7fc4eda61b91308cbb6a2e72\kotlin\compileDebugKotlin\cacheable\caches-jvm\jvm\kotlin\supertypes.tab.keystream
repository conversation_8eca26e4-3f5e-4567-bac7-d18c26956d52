Acom.brentvatne.common.api.BufferingStrategy.BufferingStrategyEnum)com.brentvatne.common.api.ResizeMode.Mode+com.brentvatne.common.api.ViewType.ViewType&com.brentvatne.common.react.EventTypes/com.brentvatne.exoplayer.AspectRatioFrameLayout$com.brentvatne.exoplayer.AudioOutput#com.brentvatne.exoplayer.DRMManager4com.brentvatne.exoplayer.DefaultReactExoplayerConfig&com.brentvatne.exoplayer.ExoPlayerView8com.brentvatne.exoplayer.ExoPlayerView.ComponentListener-com.brentvatne.exoplayer.FullScreenPlayerViewAcom.brentvatne.exoplayer.FullScreenPlayerView.KeepScreenOnUpdater+com.brentvatne.exoplayer.RNVExoplayerPlugin>com.brentvatne.exoplayer.ReactExoplayerLoadErrorHandlingPolicy2com.brentvatne.exoplayer.ReactExoplayerViewManager.com.brentvatne.exoplayer.VideoPlaybackCallback.com.brentvatne.exoplayer.PlaybackServiceBinder-com.brentvatne.exoplayer.VideoPlaybackService?com.brentvatne.exoplayer.VideoPlaybackService.Companion.COMMAND,com.brentvatne.react.ReactNativeVideoManager&com.brentvatne.react.ReactVideoPackage+com.brentvatne.react.VideoDecoderInfoModule'com.brentvatne.react.VideoManagerModule2com.brentvatne.receiver.AudioBecomingNoisyReceiver0com.brentvatne.receiver.PictureInPictureReceiver                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             