/**
 * Automatically generated file. DO NOT MODIFY
 */
package com.brentvatne.react;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String LIBRARY_PACKAGE_NAME = "com.brentvatne.react";
  public static final String BUILD_TYPE = "debug";
  // Field from default config.
  public static final boolean IS_NEW_ARCHITECTURE_ENABLED = true;
  // Field from default config.
  public static final boolean USE_EXOPLAYER_DASH = true;
  // Field from default config.
  public static final boolean USE_EXOPLAYER_HLS = true;
  // Field from default config.
  public static final boolean USE_EXOPLAYER_IMA = false;
  // Field from default config.
  public static final boolean USE_EXOPLAYER_RTSP = false;
  // Field from default config.
  public static final boolean USE_EXOPLAYER_SMOOTH_STREAMING = true;
}
