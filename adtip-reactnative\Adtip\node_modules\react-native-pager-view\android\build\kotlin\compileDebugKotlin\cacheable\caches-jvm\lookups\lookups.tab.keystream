  Context android.content  ContextWrapper android.content  baseContext android.content.ContextWrapper  Handler 
android.os  Looper 
android.os  AttributeSet android.util  
Choreographer android.view  MotionEvent android.view  View android.view  ViewConfiguration android.view  	ViewGroup android.view  
ViewParent android.view  
FrameCallback android.view.Choreographer  getInstance android.view.Choreographer  postFrameCallback android.view.Choreographer  removeFrameCallback android.view.Choreographer  let (android.view.Choreographer.FrameCallback  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  actionMasked android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  IllegalArgumentException android.view.View  LAYOUT_DIRECTION_LTR android.view.View  LAYOUT_DIRECTION_RTL android.view.View  MotionEvent android.view.View  NativeGestureUtil android.view.View  ORIENTATION_HORIZONTAL android.view.View  OVER_SCROLL_ALWAYS android.view.View  OVER_SCROLL_IF_CONTENT_SCROLLS android.view.View  OVER_SCROLL_NEVER android.view.View  View android.view.View  ViewConfiguration android.view.View  
ViewPager2 android.view.View  
absoluteValue android.view.View  bottom android.view.View  canScrollHorizontally android.view.View  canScrollVertically android.view.View  context android.view.View  generateViewId android.view.View  
getContext android.view.View  height android.view.View  id android.view.View  
isSaveEnabled android.view.View  layout android.view.View  layoutDirection android.view.View  layoutParams android.view.View  left android.view.View  measure android.view.View  notifyNativeGestureEnded android.view.View  notifyNativeGestureStarted android.view.View  onTouchEvent android.view.View  overScrollMode android.view.View  parent android.view.View  post android.view.View  right android.view.View  sign android.view.View  top android.view.View  translationX android.view.View  translationY android.view.View  width android.view.View  EXACTLY android.view.View.MeasureSpec  makeMeasureSpec android.view.View.MeasureSpec  get android.view.ViewConfiguration  scaledTouchSlop android.view.ViewConfiguration  IllegalArgumentException android.view.ViewGroup  LayoutParams android.view.ViewGroup  MotionEvent android.view.ViewGroup  NativeGestureUtil android.view.ViewGroup  ORIENTATION_HORIZONTAL android.view.ViewGroup  View android.view.ViewGroup  ViewConfiguration android.view.ViewGroup  
ViewPager2 android.view.ViewGroup  
absoluteValue android.view.ViewGroup  addView android.view.ViewGroup  
childCount android.view.ViewGroup  context android.view.ViewGroup  
getChildAt android.view.ViewGroup  notifyNativeGestureEnded android.view.ViewGroup  notifyNativeGestureStarted android.view.ViewGroup  onInterceptTouchEvent android.view.ViewGroup  removeAllViews android.view.ViewGroup  
removeView android.view.ViewGroup  sign android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  parent android.view.ViewParent  "requestDisallowInterceptTouchEvent android.view.ViewParent  FrameLayout android.widget  IllegalArgumentException android.widget.FrameLayout  MotionEvent android.widget.FrameLayout  NativeGestureUtil android.widget.FrameLayout  ORIENTATION_HORIZONTAL android.widget.FrameLayout  View android.widget.FrameLayout  ViewConfiguration android.widget.FrameLayout  
ViewPager2 android.widget.FrameLayout  
absoluteValue android.widget.FrameLayout  addView android.widget.FrameLayout  
childCount android.widget.FrameLayout  
isSaveEnabled android.widget.FrameLayout  layoutParams android.widget.FrameLayout  notifyNativeGestureEnded android.widget.FrameLayout  notifyNativeGestureStarted android.widget.FrameLayout  onInterceptTouchEvent android.widget.FrameLayout  onTouchEvent android.widget.FrameLayout  removeAllViews android.widget.FrameLayout  
removeView android.widget.FrameLayout  sign android.widget.FrameLayout  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  	ArrayList 1androidx.recyclerview.widget.RecyclerView.Adapter  FrameLayout 1androidx.recyclerview.widget.RecyclerView.Adapter  View 1androidx.recyclerview.widget.RecyclerView.Adapter  	ViewGroup 1androidx.recyclerview.widget.RecyclerView.Adapter  ViewPagerViewHolder 1androidx.recyclerview.widget.RecyclerView.Adapter  create 1androidx.recyclerview.widget.RecyclerView.Adapter  	itemCount 1androidx.recyclerview.widget.RecyclerView.Adapter  FrameLayout 4androidx.recyclerview.widget.RecyclerView.ViewHolder  MATCH_PARENT 4androidx.recyclerview.widget.RecyclerView.ViewHolder  	ViewGroup 4androidx.recyclerview.widget.RecyclerView.ViewHolder  ViewPagerViewHolder 4androidx.recyclerview.widget.RecyclerView.ViewHolder  itemView 4androidx.recyclerview.widget.RecyclerView.ViewHolder  setIsRecyclable 4androidx.recyclerview.widget.RecyclerView.ViewHolder  
ViewPager2 androidx.viewpager2.widget  OFFSCREEN_PAGE_LIMIT_DEFAULT %androidx.viewpager2.widget.ViewPager2  ORIENTATION_HORIZONTAL %androidx.viewpager2.widget.ViewPager2  ORIENTATION_VERTICAL %androidx.viewpager2.widget.ViewPager2  OVER_SCROLL_ALWAYS %androidx.viewpager2.widget.ViewPager2  OVER_SCROLL_IF_CONTENT_SCROLLS %androidx.viewpager2.widget.ViewPager2  OVER_SCROLL_NEVER %androidx.viewpager2.widget.ViewPager2  OnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  PageTransformer %androidx.viewpager2.widget.ViewPager2  SCROLL_STATE_DRAGGING %androidx.viewpager2.widget.ViewPager2  SCROLL_STATE_IDLE %androidx.viewpager2.widget.ViewPager2  SCROLL_STATE_SETTLING %androidx.viewpager2.widget.ViewPager2  adapter %androidx.viewpager2.widget.ViewPager2  currentItem %androidx.viewpager2.widget.ViewPager2  
getChildAt %androidx.viewpager2.widget.ViewPager2  
isSaveEnabled %androidx.viewpager2.widget.ViewPager2  isUserInputEnabled %androidx.viewpager2.widget.ViewPager2  layoutDirection %androidx.viewpager2.widget.ViewPager2  offscreenPageLimit %androidx.viewpager2.widget.ViewPager2  orientation %androidx.viewpager2.widget.ViewPager2  post %androidx.viewpager2.widget.ViewPager2  registerOnPageChangeCallback %androidx.viewpager2.widget.ViewPager2  setCurrentItem %androidx.viewpager2.widget.ViewPager2  setPageTransformer %androidx.viewpager2.widget.ViewPager2  IllegalStateException :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  PageScrollEvent :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  PageScrollStateChangedEvent :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  PageSelectedEvent :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  String :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  UIManagerHelper :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  
ViewPager2 :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageScrollStateChanged :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageScrolled :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  onPageSelected :androidx.viewpager2.widget.ViewPager2.OnPageChangeCallback  <SAM-CONSTRUCTOR> 5androidx.viewpager2.widget.ViewPager2.PageTransformer  
Assertions com.facebook.infer.annotation  
assertNotNull (com.facebook.infer.annotation.Assertions  ReactPackage com.facebook.react  	Arguments com.facebook.react.bridge  NativeModule com.facebook.react.bridge  ReactApplicationContext com.facebook.react.bridge  ReactContext com.facebook.react.bridge  
ReadableArray com.facebook.react.bridge  WritableMap com.facebook.react.bridge  	createMap #com.facebook.react.bridge.Arguments  
Assertions (com.facebook.react.bridge.BaseJavaModule  BuildConfig (com.facebook.react.bridge.BaseJavaModule  Float (com.facebook.react.bridge.BaseJavaModule  IllegalStateException (com.facebook.react.bridge.BaseJavaModule  Int (com.facebook.react.bridge.BaseJavaModule  
MapBuilder (com.facebook.react.bridge.BaseJavaModule  NestedScrollableHost (com.facebook.react.bridge.BaseJavaModule  OnPageChangeCallback (com.facebook.react.bridge.BaseJavaModule  PageScrollEvent (com.facebook.react.bridge.BaseJavaModule  PageScrollStateChangedEvent (com.facebook.react.bridge.BaseJavaModule  PageSelectedEvent (com.facebook.react.bridge.BaseJavaModule  PagerViewViewManagerImpl (com.facebook.react.bridge.BaseJavaModule  RNCViewPagerManagerDelegate (com.facebook.react.bridge.BaseJavaModule  SoLoader (com.facebook.react.bridge.BaseJavaModule  String (com.facebook.react.bridge.BaseJavaModule  UIManagerHelper (com.facebook.react.bridge.BaseJavaModule  View (com.facebook.react.bridge.BaseJavaModule  	ViewGroup (com.facebook.react.bridge.BaseJavaModule  
ViewPager2 (com.facebook.react.bridge.BaseJavaModule  ViewPagerAdapter (com.facebook.react.bridge.BaseJavaModule  addView (com.facebook.react.bridge.BaseJavaModule  
getChildAt (com.facebook.react.bridge.BaseJavaModule  
getChildCount (com.facebook.react.bridge.BaseJavaModule  getViewPager (com.facebook.react.bridge.BaseJavaModule  needsCustomLayoutForChildren (com.facebook.react.bridge.BaseJavaModule  removeAllViews (com.facebook.react.bridge.BaseJavaModule  
removeView (com.facebook.react.bridge.BaseJavaModule  removeViewAt (com.facebook.react.bridge.BaseJavaModule  setCurrentItem (com.facebook.react.bridge.BaseJavaModule  setInitialPage (com.facebook.react.bridge.BaseJavaModule  setLayoutDirection (com.facebook.react.bridge.BaseJavaModule  setOffscreenPageLimit (com.facebook.react.bridge.BaseJavaModule  setOrientation (com.facebook.react.bridge.BaseJavaModule  setOverScrollMode (com.facebook.react.bridge.BaseJavaModule  
setPageMargin (com.facebook.react.bridge.BaseJavaModule  setScrollEnabled (com.facebook.react.bridge.BaseJavaModule  	putDouble %com.facebook.react.bridge.WritableMap  putInt %com.facebook.react.bridge.WritableMap  	putString %com.facebook.react.bridge.WritableMap  
MapBuilder com.facebook.react.common  of $com.facebook.react.common.MapBuilder  ReactModule %com.facebook.react.module.annotations  
Assertions com.facebook.react.uimanager  Boolean com.facebook.react.uimanager  BuildConfig com.facebook.react.uimanager  Float com.facebook.react.uimanager  IllegalStateException com.facebook.react.uimanager  Int com.facebook.react.uimanager  Map com.facebook.react.uimanager  
MapBuilder com.facebook.react.uimanager  
MutableMap com.facebook.react.uimanager  NestedScrollableHost com.facebook.react.uimanager  OnPageChangeCallback com.facebook.react.uimanager  PageScrollEvent com.facebook.react.uimanager  PageScrollStateChangedEvent com.facebook.react.uimanager  PageSelectedEvent com.facebook.react.uimanager  PagerViewViewManagerImpl com.facebook.react.uimanager  	PixelUtil com.facebook.react.uimanager  RNCViewPagerManagerDelegate com.facebook.react.uimanager  RNCViewPagerManagerInterface com.facebook.react.uimanager  ReactModule com.facebook.react.uimanager  	ReactProp com.facebook.react.uimanager  
ReadableArray com.facebook.react.uimanager  SoLoader com.facebook.react.uimanager  String com.facebook.react.uimanager  ThemedReactContext com.facebook.react.uimanager  UIManagerHelper com.facebook.react.uimanager  View com.facebook.react.uimanager  	ViewGroup com.facebook.react.uimanager  ViewGroupManager com.facebook.react.uimanager  ViewManager com.facebook.react.uimanager  ViewManagerDelegate com.facebook.react.uimanager  
ViewPager2 com.facebook.react.uimanager  ViewPagerAdapter com.facebook.react.uimanager  addView com.facebook.react.uimanager  
getChildAt com.facebook.react.uimanager  
getChildCount com.facebook.react.uimanager  getViewPager com.facebook.react.uimanager  needsCustomLayoutForChildren com.facebook.react.uimanager  removeAllViews com.facebook.react.uimanager  
removeView com.facebook.react.uimanager  removeViewAt com.facebook.react.uimanager  setCurrentItem com.facebook.react.uimanager  setInitialPage com.facebook.react.uimanager  setLayoutDirection com.facebook.react.uimanager  setOffscreenPageLimit com.facebook.react.uimanager  setOrientation com.facebook.react.uimanager  setOverScrollMode com.facebook.react.uimanager  
setPageMargin com.facebook.react.uimanager  setScrollEnabled com.facebook.react.uimanager  
Assertions ,com.facebook.react.uimanager.BaseViewManager  BuildConfig ,com.facebook.react.uimanager.BaseViewManager  Float ,com.facebook.react.uimanager.BaseViewManager  IllegalStateException ,com.facebook.react.uimanager.BaseViewManager  Int ,com.facebook.react.uimanager.BaseViewManager  
MapBuilder ,com.facebook.react.uimanager.BaseViewManager  NestedScrollableHost ,com.facebook.react.uimanager.BaseViewManager  OnPageChangeCallback ,com.facebook.react.uimanager.BaseViewManager  PageScrollEvent ,com.facebook.react.uimanager.BaseViewManager  PageScrollStateChangedEvent ,com.facebook.react.uimanager.BaseViewManager  PageSelectedEvent ,com.facebook.react.uimanager.BaseViewManager  PagerViewViewManagerImpl ,com.facebook.react.uimanager.BaseViewManager  RNCViewPagerManagerDelegate ,com.facebook.react.uimanager.BaseViewManager  SoLoader ,com.facebook.react.uimanager.BaseViewManager  String ,com.facebook.react.uimanager.BaseViewManager  UIManagerHelper ,com.facebook.react.uimanager.BaseViewManager  View ,com.facebook.react.uimanager.BaseViewManager  	ViewGroup ,com.facebook.react.uimanager.BaseViewManager  
ViewPager2 ,com.facebook.react.uimanager.BaseViewManager  ViewPagerAdapter ,com.facebook.react.uimanager.BaseViewManager  addView ,com.facebook.react.uimanager.BaseViewManager  
getChildAt ,com.facebook.react.uimanager.BaseViewManager  
getChildCount ,com.facebook.react.uimanager.BaseViewManager  getViewPager ,com.facebook.react.uimanager.BaseViewManager  needsCustomLayoutForChildren ,com.facebook.react.uimanager.BaseViewManager  removeAllViews ,com.facebook.react.uimanager.BaseViewManager  
removeView ,com.facebook.react.uimanager.BaseViewManager  removeViewAt ,com.facebook.react.uimanager.BaseViewManager  setCurrentItem ,com.facebook.react.uimanager.BaseViewManager  setInitialPage ,com.facebook.react.uimanager.BaseViewManager  setLayoutDirection ,com.facebook.react.uimanager.BaseViewManager  setOffscreenPageLimit ,com.facebook.react.uimanager.BaseViewManager  setOrientation ,com.facebook.react.uimanager.BaseViewManager  setOverScrollMode ,com.facebook.react.uimanager.BaseViewManager  
setPageMargin ,com.facebook.react.uimanager.BaseViewManager  setScrollEnabled ,com.facebook.react.uimanager.BaseViewManager  toPixelFromDIP &com.facebook.react.uimanager.PixelUtil  getEventDispatcherForReactTag ,com.facebook.react.uimanager.UIManagerHelper  
Assertions -com.facebook.react.uimanager.ViewGroupManager  BuildConfig -com.facebook.react.uimanager.ViewGroupManager  Float -com.facebook.react.uimanager.ViewGroupManager  IllegalStateException -com.facebook.react.uimanager.ViewGroupManager  Int -com.facebook.react.uimanager.ViewGroupManager  
MapBuilder -com.facebook.react.uimanager.ViewGroupManager  NestedScrollableHost -com.facebook.react.uimanager.ViewGroupManager  OnPageChangeCallback -com.facebook.react.uimanager.ViewGroupManager  PageScrollEvent -com.facebook.react.uimanager.ViewGroupManager  PageScrollStateChangedEvent -com.facebook.react.uimanager.ViewGroupManager  PageSelectedEvent -com.facebook.react.uimanager.ViewGroupManager  PagerViewViewManagerImpl -com.facebook.react.uimanager.ViewGroupManager  RNCViewPagerManagerDelegate -com.facebook.react.uimanager.ViewGroupManager  SoLoader -com.facebook.react.uimanager.ViewGroupManager  String -com.facebook.react.uimanager.ViewGroupManager  UIManagerHelper -com.facebook.react.uimanager.ViewGroupManager  View -com.facebook.react.uimanager.ViewGroupManager  	ViewGroup -com.facebook.react.uimanager.ViewGroupManager  
ViewPager2 -com.facebook.react.uimanager.ViewGroupManager  ViewPagerAdapter -com.facebook.react.uimanager.ViewGroupManager  addView -com.facebook.react.uimanager.ViewGroupManager  
getChildAt -com.facebook.react.uimanager.ViewGroupManager  
getChildCount -com.facebook.react.uimanager.ViewGroupManager  getViewPager -com.facebook.react.uimanager.ViewGroupManager  needsCustomLayoutForChildren -com.facebook.react.uimanager.ViewGroupManager  removeAllViews -com.facebook.react.uimanager.ViewGroupManager  
removeView -com.facebook.react.uimanager.ViewGroupManager  removeViewAt -com.facebook.react.uimanager.ViewGroupManager  setCurrentItem -com.facebook.react.uimanager.ViewGroupManager  setInitialPage -com.facebook.react.uimanager.ViewGroupManager  setLayoutDirection -com.facebook.react.uimanager.ViewGroupManager  setOffscreenPageLimit -com.facebook.react.uimanager.ViewGroupManager  setOrientation -com.facebook.react.uimanager.ViewGroupManager  setOverScrollMode -com.facebook.react.uimanager.ViewGroupManager  
setPageMargin -com.facebook.react.uimanager.ViewGroupManager  setScrollEnabled -com.facebook.react.uimanager.ViewGroupManager  
Assertions (com.facebook.react.uimanager.ViewManager  BuildConfig (com.facebook.react.uimanager.ViewManager  Float (com.facebook.react.uimanager.ViewManager  IllegalStateException (com.facebook.react.uimanager.ViewManager  Int (com.facebook.react.uimanager.ViewManager  
MapBuilder (com.facebook.react.uimanager.ViewManager  NestedScrollableHost (com.facebook.react.uimanager.ViewManager  OnPageChangeCallback (com.facebook.react.uimanager.ViewManager  PageScrollEvent (com.facebook.react.uimanager.ViewManager  PageScrollStateChangedEvent (com.facebook.react.uimanager.ViewManager  PageSelectedEvent (com.facebook.react.uimanager.ViewManager  PagerViewViewManagerImpl (com.facebook.react.uimanager.ViewManager  RNCViewPagerManagerDelegate (com.facebook.react.uimanager.ViewManager  SoLoader (com.facebook.react.uimanager.ViewManager  String (com.facebook.react.uimanager.ViewManager  UIManagerHelper (com.facebook.react.uimanager.ViewManager  View (com.facebook.react.uimanager.ViewManager  	ViewGroup (com.facebook.react.uimanager.ViewManager  
ViewPager2 (com.facebook.react.uimanager.ViewManager  ViewPagerAdapter (com.facebook.react.uimanager.ViewManager  addView (com.facebook.react.uimanager.ViewManager  
getChildAt (com.facebook.react.uimanager.ViewManager  
getChildCount (com.facebook.react.uimanager.ViewManager  getViewPager (com.facebook.react.uimanager.ViewManager  	mDelegate (com.facebook.react.uimanager.ViewManager  needsCustomLayoutForChildren (com.facebook.react.uimanager.ViewManager  removeAllViews (com.facebook.react.uimanager.ViewManager  
removeView (com.facebook.react.uimanager.ViewManager  removeViewAt (com.facebook.react.uimanager.ViewManager  setCurrentItem (com.facebook.react.uimanager.ViewManager  setInitialPage (com.facebook.react.uimanager.ViewManager  setLayoutDirection (com.facebook.react.uimanager.ViewManager  setOffscreenPageLimit (com.facebook.react.uimanager.ViewManager  setOrientation (com.facebook.react.uimanager.ViewManager  setOverScrollMode (com.facebook.react.uimanager.ViewManager  
setPageMargin (com.facebook.react.uimanager.ViewManager  setScrollEnabled (com.facebook.react.uimanager.ViewManager  receiveCommand 0com.facebook.react.uimanager.ViewManagerDelegate  	ReactProp (com.facebook.react.uimanager.annotations  Event #com.facebook.react.uimanager.events  EventDispatcher #com.facebook.react.uimanager.events  NativeGestureUtil #com.facebook.react.uimanager.events  RCTEventEmitter #com.facebook.react.uimanager.events  	Arguments )com.facebook.react.uimanager.events.Event  
EVENT_NAME )com.facebook.react.uimanager.events.Event  
isInfinite )com.facebook.react.uimanager.events.Event  isNaN )com.facebook.react.uimanager.events.Event  viewTag )com.facebook.react.uimanager.events.Event  
dispatchEvent 3com.facebook.react.uimanager.events.EventDispatcher  notifyNativeGestureEnded 5com.facebook.react.uimanager.events.NativeGestureUtil  notifyNativeGestureStarted 5com.facebook.react.uimanager.events.NativeGestureUtil  receiveEvent 3com.facebook.react.uimanager.events.RCTEventEmitter  RNCViewPagerManagerDelegate com.facebook.react.viewmanagers  RNCViewPagerManagerInterface com.facebook.react.viewmanagers  SoLoader com.facebook.soloader  loadLibrary com.facebook.soloader.SoLoader  Adapter com.reactnativepagerview  	ArrayList com.reactnativepagerview  
Assertions com.reactnativepagerview  AttributeSet com.reactnativepagerview  Boolean com.reactnativepagerview  BuildConfig com.reactnativepagerview  
Choreographer com.reactnativepagerview  ClassNotFoundException com.reactnativepagerview  Context com.reactnativepagerview  ContextWrapper com.reactnativepagerview  Float com.reactnativepagerview  FrameLayout com.reactnativepagerview  Helper com.reactnativepagerview  IllegalArgumentException com.reactnativepagerview  IllegalStateException com.reactnativepagerview  Int com.reactnativepagerview  List com.reactnativepagerview  MATCH_PARENT com.reactnativepagerview  Map com.reactnativepagerview  
MapBuilder com.reactnativepagerview  MotionEvent com.reactnativepagerview  
MutableMap com.reactnativepagerview  NativeGestureUtil com.reactnativepagerview  NativeModule com.reactnativepagerview  NestedScrollableHost com.reactnativepagerview  ORIENTATION_HORIZONTAL com.reactnativepagerview  OnPageChangeCallback com.reactnativepagerview  PageScrollEvent com.reactnativepagerview  PageScrollStateChangedEvent com.reactnativepagerview  PageSelectedEvent com.reactnativepagerview  PagerViewPackage com.reactnativepagerview  PagerViewViewManager com.reactnativepagerview  PagerViewViewManagerImpl com.reactnativepagerview  	PixelUtil com.reactnativepagerview  RNCViewPagerManagerDelegate com.reactnativepagerview  RNCViewPagerManagerInterface com.reactnativepagerview  ReactApplicationContext com.reactnativepagerview  ReactContext com.reactnativepagerview  ReactModule com.reactnativepagerview  ReactPackage com.reactnativepagerview  	ReactProp com.reactnativepagerview  
ReadableArray com.reactnativepagerview  SoLoader com.reactnativepagerview  String com.reactnativepagerview  ThemedReactContext com.reactnativepagerview  UIManagerHelper com.reactnativepagerview  View com.reactnativepagerview  ViewConfiguration com.reactnativepagerview  	ViewGroup com.reactnativepagerview  ViewGroupManager com.reactnativepagerview  
ViewHolder com.reactnativepagerview  ViewManager com.reactnativepagerview  ViewManagerDelegate com.reactnativepagerview  
ViewPager2 com.reactnativepagerview  ViewPagerAdapter com.reactnativepagerview  ViewPagerViewHolder com.reactnativepagerview  addView com.reactnativepagerview  create com.reactnativepagerview  	emptyList com.reactnativepagerview  
getChildAt com.reactnativepagerview  
getChildCount com.reactnativepagerview  getViewPager com.reactnativepagerview  let com.reactnativepagerview  listOf com.reactnativepagerview  needsCustomLayoutForChildren com.reactnativepagerview  notifyNativeGestureEnded com.reactnativepagerview  notifyNativeGestureStarted com.reactnativepagerview  removeAllViews com.reactnativepagerview  
removeView com.reactnativepagerview  removeViewAt com.reactnativepagerview  setCurrentItem com.reactnativepagerview  setInitialPage com.reactnativepagerview  setLayoutDirection com.reactnativepagerview  setOffscreenPageLimit com.reactnativepagerview  setOrientation com.reactnativepagerview  setOverScrollMode com.reactnativepagerview  
setPageMargin com.reactnativepagerview  setScrollEnabled com.reactnativepagerview  toPixelFromDIP com.reactnativepagerview  CODEGEN_MODULE_REGISTRATION $com.reactnativepagerview.BuildConfig  
FrameCallback &com.reactnativepagerview.Choreographer  Context com.reactnativepagerview.Helper  ContextWrapper com.reactnativepagerview.Helper  ReactContext com.reactnativepagerview.Helper  View com.reactnativepagerview.Helper  IllegalArgumentException -com.reactnativepagerview.NestedScrollableHost  MotionEvent -com.reactnativepagerview.NestedScrollableHost  NativeGestureUtil -com.reactnativepagerview.NestedScrollableHost  ORIENTATION_HORIZONTAL -com.reactnativepagerview.NestedScrollableHost  ViewConfiguration -com.reactnativepagerview.NestedScrollableHost  
absoluteValue -com.reactnativepagerview.NestedScrollableHost  addView -com.reactnativepagerview.NestedScrollableHost  canChildScroll -com.reactnativepagerview.NestedScrollableHost  child -com.reactnativepagerview.NestedScrollableHost  
childCount -com.reactnativepagerview.NestedScrollableHost  context -com.reactnativepagerview.NestedScrollableHost  didSetInitialIndex -com.reactnativepagerview.NestedScrollableHost  
getChildAt -com.reactnativepagerview.NestedScrollableHost  handleInterceptTouchEvent -com.reactnativepagerview.NestedScrollableHost  id -com.reactnativepagerview.NestedScrollableHost  initialIndex -com.reactnativepagerview.NestedScrollableHost  initialX -com.reactnativepagerview.NestedScrollableHost  initialY -com.reactnativepagerview.NestedScrollableHost  
isSaveEnabled -com.reactnativepagerview.NestedScrollableHost  layoutParams -com.reactnativepagerview.NestedScrollableHost  nativeGestureStarted -com.reactnativepagerview.NestedScrollableHost  notifyNativeGestureEnded -com.reactnativepagerview.NestedScrollableHost  notifyNativeGestureStarted -com.reactnativepagerview.NestedScrollableHost  parent -com.reactnativepagerview.NestedScrollableHost  parentViewPager -com.reactnativepagerview.NestedScrollableHost  sign -com.reactnativepagerview.NestedScrollableHost  	touchSlop -com.reactnativepagerview.NestedScrollableHost  PagerViewViewManager )com.reactnativepagerview.PagerViewPackage  	emptyList )com.reactnativepagerview.PagerViewPackage  listOf )com.reactnativepagerview.PagerViewPackage  
Assertions -com.reactnativepagerview.PagerViewViewManager  Boolean -com.reactnativepagerview.PagerViewViewManager  BuildConfig -com.reactnativepagerview.PagerViewViewManager  Float -com.reactnativepagerview.PagerViewViewManager  IllegalStateException -com.reactnativepagerview.PagerViewViewManager  Int -com.reactnativepagerview.PagerViewViewManager  Map -com.reactnativepagerview.PagerViewViewManager  
MapBuilder -com.reactnativepagerview.PagerViewViewManager  
MutableMap -com.reactnativepagerview.PagerViewViewManager  NestedScrollableHost -com.reactnativepagerview.PagerViewViewManager  OnPageChangeCallback -com.reactnativepagerview.PagerViewViewManager  PageScrollEvent -com.reactnativepagerview.PagerViewViewManager  PageScrollStateChangedEvent -com.reactnativepagerview.PagerViewViewManager  PageSelectedEvent -com.reactnativepagerview.PagerViewViewManager  PagerViewViewManagerImpl -com.reactnativepagerview.PagerViewViewManager  RNCViewPagerManagerDelegate -com.reactnativepagerview.PagerViewViewManager  	ReactProp -com.reactnativepagerview.PagerViewViewManager  
ReadableArray -com.reactnativepagerview.PagerViewViewManager  SoLoader -com.reactnativepagerview.PagerViewViewManager  String -com.reactnativepagerview.PagerViewViewManager  ThemedReactContext -com.reactnativepagerview.PagerViewViewManager  UIManagerHelper -com.reactnativepagerview.PagerViewViewManager  View -com.reactnativepagerview.PagerViewViewManager  	ViewGroup -com.reactnativepagerview.PagerViewViewManager  ViewManagerDelegate -com.reactnativepagerview.PagerViewViewManager  
ViewPager2 -com.reactnativepagerview.PagerViewViewManager  ViewPagerAdapter -com.reactnativepagerview.PagerViewViewManager  addView -com.reactnativepagerview.PagerViewViewManager  
getChildAt -com.reactnativepagerview.PagerViewViewManager  
getChildCount -com.reactnativepagerview.PagerViewViewManager  getViewPager -com.reactnativepagerview.PagerViewViewManager  goTo -com.reactnativepagerview.PagerViewViewManager  	mDelegate -com.reactnativepagerview.PagerViewViewManager  needsCustomLayoutForChildren -com.reactnativepagerview.PagerViewViewManager  removeAllViews -com.reactnativepagerview.PagerViewViewManager  
removeView -com.reactnativepagerview.PagerViewViewManager  removeViewAt -com.reactnativepagerview.PagerViewViewManager  setCurrentItem -com.reactnativepagerview.PagerViewViewManager  setInitialPage -com.reactnativepagerview.PagerViewViewManager  setLayoutDirection -com.reactnativepagerview.PagerViewViewManager  setOffscreenPageLimit -com.reactnativepagerview.PagerViewViewManager  setOrientation -com.reactnativepagerview.PagerViewViewManager  setOverScrollMode -com.reactnativepagerview.PagerViewViewManager  
setPageMargin -com.reactnativepagerview.PagerViewViewManager  setScrollEnabled -com.reactnativepagerview.PagerViewViewManager  
Assertions 7com.reactnativepagerview.PagerViewViewManager.Companion  BuildConfig 7com.reactnativepagerview.PagerViewViewManager.Companion  IllegalStateException 7com.reactnativepagerview.PagerViewViewManager.Companion  
MapBuilder 7com.reactnativepagerview.PagerViewViewManager.Companion  NestedScrollableHost 7com.reactnativepagerview.PagerViewViewManager.Companion  PageScrollEvent 7com.reactnativepagerview.PagerViewViewManager.Companion  PageScrollStateChangedEvent 7com.reactnativepagerview.PagerViewViewManager.Companion  PageSelectedEvent 7com.reactnativepagerview.PagerViewViewManager.Companion  PagerViewViewManagerImpl 7com.reactnativepagerview.PagerViewViewManager.Companion  RNCViewPagerManagerDelegate 7com.reactnativepagerview.PagerViewViewManager.Companion  SoLoader 7com.reactnativepagerview.PagerViewViewManager.Companion  UIManagerHelper 7com.reactnativepagerview.PagerViewViewManager.Companion  View 7com.reactnativepagerview.PagerViewViewManager.Companion  	ViewGroup 7com.reactnativepagerview.PagerViewViewManager.Companion  
ViewPager2 7com.reactnativepagerview.PagerViewViewManager.Companion  ViewPagerAdapter 7com.reactnativepagerview.PagerViewViewManager.Companion  addView 7com.reactnativepagerview.PagerViewViewManager.Companion  
getChildAt 7com.reactnativepagerview.PagerViewViewManager.Companion  
getChildCount 7com.reactnativepagerview.PagerViewViewManager.Companion  getViewPager 7com.reactnativepagerview.PagerViewViewManager.Companion  needsCustomLayoutForChildren 7com.reactnativepagerview.PagerViewViewManager.Companion  removeAllViews 7com.reactnativepagerview.PagerViewViewManager.Companion  
removeView 7com.reactnativepagerview.PagerViewViewManager.Companion  removeViewAt 7com.reactnativepagerview.PagerViewViewManager.Companion  setCurrentItem 7com.reactnativepagerview.PagerViewViewManager.Companion  setInitialPage 7com.reactnativepagerview.PagerViewViewManager.Companion  setLayoutDirection 7com.reactnativepagerview.PagerViewViewManager.Companion  setOffscreenPageLimit 7com.reactnativepagerview.PagerViewViewManager.Companion  setOrientation 7com.reactnativepagerview.PagerViewViewManager.Companion  setOverScrollMode 7com.reactnativepagerview.PagerViewViewManager.Companion  
setPageMargin 7com.reactnativepagerview.PagerViewViewManager.Companion  setScrollEnabled 7com.reactnativepagerview.PagerViewViewManager.Companion  
Choreographer 1com.reactnativepagerview.PagerViewViewManagerImpl  ClassNotFoundException 1com.reactnativepagerview.PagerViewViewManagerImpl  NAME 1com.reactnativepagerview.PagerViewViewManagerImpl  	PixelUtil 1com.reactnativepagerview.PagerViewViewManagerImpl  View 1com.reactnativepagerview.PagerViewViewManagerImpl  
ViewPager2 1com.reactnativepagerview.PagerViewViewManagerImpl  addView 1com.reactnativepagerview.PagerViewViewManagerImpl  "debouncedRefreshViewChildrenLayout 1com.reactnativepagerview.PagerViewViewManagerImpl  
getChildAt 1com.reactnativepagerview.PagerViewViewManagerImpl  
getChildCount 1com.reactnativepagerview.PagerViewViewManagerImpl  getViewPager 1com.reactnativepagerview.PagerViewViewManagerImpl  let 1com.reactnativepagerview.PagerViewViewManagerImpl  needsCustomLayoutForChildren 1com.reactnativepagerview.PagerViewViewManagerImpl  refreshFrameCallback 1com.reactnativepagerview.PagerViewViewManagerImpl  refreshViewChildrenLayout 1com.reactnativepagerview.PagerViewViewManagerImpl  removeAllViews 1com.reactnativepagerview.PagerViewViewManagerImpl  
removeView 1com.reactnativepagerview.PagerViewViewManagerImpl  removeViewAt 1com.reactnativepagerview.PagerViewViewManagerImpl  setCurrentItem 1com.reactnativepagerview.PagerViewViewManagerImpl  setInitialPage 1com.reactnativepagerview.PagerViewViewManagerImpl  setLayoutDirection 1com.reactnativepagerview.PagerViewViewManagerImpl  setOffscreenPageLimit 1com.reactnativepagerview.PagerViewViewManagerImpl  setOrientation 1com.reactnativepagerview.PagerViewViewManagerImpl  setOverScrollMode 1com.reactnativepagerview.PagerViewViewManagerImpl  
setPageMargin 1com.reactnativepagerview.PagerViewViewManagerImpl  setScrollEnabled 1com.reactnativepagerview.PagerViewViewManagerImpl  toPixelFromDIP 1com.reactnativepagerview.PagerViewViewManagerImpl  	ArrayList )com.reactnativepagerview.ViewPagerAdapter  ViewPagerViewHolder )com.reactnativepagerview.ViewPagerAdapter  addChild )com.reactnativepagerview.ViewPagerAdapter  
childrenViews )com.reactnativepagerview.ViewPagerAdapter  create )com.reactnativepagerview.ViewPagerAdapter  
getChildAt )com.reactnativepagerview.ViewPagerAdapter  	itemCount )com.reactnativepagerview.ViewPagerAdapter  notifyItemInserted )com.reactnativepagerview.ViewPagerAdapter  notifyItemRangeRemoved )com.reactnativepagerview.ViewPagerAdapter  notifyItemRemoved )com.reactnativepagerview.ViewPagerAdapter  	removeAll )com.reactnativepagerview.ViewPagerAdapter  removeChild )com.reactnativepagerview.ViewPagerAdapter  
removeChildAt )com.reactnativepagerview.ViewPagerAdapter  	Companion ,com.reactnativepagerview.ViewPagerViewHolder  FrameLayout ,com.reactnativepagerview.ViewPagerViewHolder  MATCH_PARENT ,com.reactnativepagerview.ViewPagerViewHolder  	ViewGroup ,com.reactnativepagerview.ViewPagerViewHolder  ViewPagerViewHolder ,com.reactnativepagerview.ViewPagerViewHolder  	container ,com.reactnativepagerview.ViewPagerViewHolder  create ,com.reactnativepagerview.ViewPagerViewHolder  itemView ,com.reactnativepagerview.ViewPagerViewHolder  setIsRecyclable ,com.reactnativepagerview.ViewPagerViewHolder  FrameLayout 6com.reactnativepagerview.ViewPagerViewHolder.Companion  MATCH_PARENT 6com.reactnativepagerview.ViewPagerViewHolder.Companion  	ViewGroup 6com.reactnativepagerview.ViewPagerViewHolder.Companion  ViewPagerViewHolder 6com.reactnativepagerview.ViewPagerViewHolder.Companion  create 6com.reactnativepagerview.ViewPagerViewHolder.Companion  	Arguments com.reactnativepagerview.event  Boolean com.reactnativepagerview.event  
EVENT_NAME com.reactnativepagerview.event  Event com.reactnativepagerview.event  Float com.reactnativepagerview.event  Int com.reactnativepagerview.event  PageScrollEvent com.reactnativepagerview.event  PageScrollStateChangedEvent com.reactnativepagerview.event  PageSelectedEvent com.reactnativepagerview.event  RCTEventEmitter com.reactnativepagerview.event  String com.reactnativepagerview.event  WritableMap com.reactnativepagerview.event  
isInfinite com.reactnativepagerview.event  isNaN com.reactnativepagerview.event  	Arguments .com.reactnativepagerview.event.PageScrollEvent  	Companion .com.reactnativepagerview.event.PageScrollEvent  
EVENT_NAME .com.reactnativepagerview.event.PageScrollEvent  Float .com.reactnativepagerview.event.PageScrollEvent  Int .com.reactnativepagerview.event.PageScrollEvent  RCTEventEmitter .com.reactnativepagerview.event.PageScrollEvent  String .com.reactnativepagerview.event.PageScrollEvent  WritableMap .com.reactnativepagerview.event.PageScrollEvent  	eventName .com.reactnativepagerview.event.PageScrollEvent  
isInfinite .com.reactnativepagerview.event.PageScrollEvent  isNaN .com.reactnativepagerview.event.PageScrollEvent  mOffset .com.reactnativepagerview.event.PageScrollEvent  	mPosition .com.reactnativepagerview.event.PageScrollEvent  serializeEventData .com.reactnativepagerview.event.PageScrollEvent  viewTag .com.reactnativepagerview.event.PageScrollEvent  	Arguments 8com.reactnativepagerview.event.PageScrollEvent.Companion  
EVENT_NAME 8com.reactnativepagerview.event.PageScrollEvent.Companion  
isInfinite 8com.reactnativepagerview.event.PageScrollEvent.Companion  isNaN 8com.reactnativepagerview.event.PageScrollEvent.Companion  	Arguments :com.reactnativepagerview.event.PageScrollStateChangedEvent  	Companion :com.reactnativepagerview.event.PageScrollStateChangedEvent  
EVENT_NAME :com.reactnativepagerview.event.PageScrollStateChangedEvent  Int :com.reactnativepagerview.event.PageScrollStateChangedEvent  RCTEventEmitter :com.reactnativepagerview.event.PageScrollStateChangedEvent  String :com.reactnativepagerview.event.PageScrollStateChangedEvent  WritableMap :com.reactnativepagerview.event.PageScrollStateChangedEvent  	eventName :com.reactnativepagerview.event.PageScrollStateChangedEvent  mPageScrollState :com.reactnativepagerview.event.PageScrollStateChangedEvent  serializeEventData :com.reactnativepagerview.event.PageScrollStateChangedEvent  viewTag :com.reactnativepagerview.event.PageScrollStateChangedEvent  	Arguments Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  
EVENT_NAME Dcom.reactnativepagerview.event.PageScrollStateChangedEvent.Companion  	Arguments 0com.reactnativepagerview.event.PageSelectedEvent  Boolean 0com.reactnativepagerview.event.PageSelectedEvent  	Companion 0com.reactnativepagerview.event.PageSelectedEvent  
EVENT_NAME 0com.reactnativepagerview.event.PageSelectedEvent  Int 0com.reactnativepagerview.event.PageSelectedEvent  RCTEventEmitter 0com.reactnativepagerview.event.PageSelectedEvent  String 0com.reactnativepagerview.event.PageSelectedEvent  WritableMap 0com.reactnativepagerview.event.PageSelectedEvent  	eventName 0com.reactnativepagerview.event.PageSelectedEvent  	mPosition 0com.reactnativepagerview.event.PageSelectedEvent  serializeEventData 0com.reactnativepagerview.event.PageSelectedEvent  viewTag 0com.reactnativepagerview.event.PageSelectedEvent  	Arguments :com.reactnativepagerview.event.PageSelectedEvent.Companion  
EVENT_NAME :com.reactnativepagerview.event.PageSelectedEvent.Companion  ClassNotFoundException 	java.lang  IllegalArgumentException 	java.lang  IllegalStateException 	java.lang  Runnable 	java.lang  
isInfinite java.lang.Float  isNaN java.lang.Float  <SAM-CONSTRUCTOR> java.lang.Runnable  Adapter 	java.util  	ArrayList 	java.util  FrameLayout 	java.util  Int 	java.util  View 	java.util  	ViewGroup 	java.util  ViewPagerViewHolder 	java.util  create 	java.util  add java.util.ArrayList  clear java.util.ArrayList  get java.util.ArrayList  indexOf java.util.ArrayList  removeAt java.util.ArrayList  size java.util.ArrayList  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Nothing kotlin  
isInfinite kotlin  isNaN kotlin  let kotlin  not kotlin.Boolean  
absoluteValue kotlin.Float  	compareTo kotlin.Float  minus kotlin.Float  sign kotlin.Float  times kotlin.Float  toDouble kotlin.Float  toInt kotlin.Float  
unaryMinus kotlin.Float  	compareTo 
kotlin.Int  minus 
kotlin.Int  rangeTo 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  
unaryMinus 
kotlin.Int  IntIterator kotlin.collections  List kotlin.collections  Map kotlin.collections  
MutableMap kotlin.collections  	emptyList kotlin.collections  listOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  
absoluteValue kotlin.math  getAbsoluteValue kotlin.math  getSign kotlin.math  sign kotlin.math  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    