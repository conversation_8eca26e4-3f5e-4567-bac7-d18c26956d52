  Activity android.app  AlarmManager android.app  NotificationManager android.app  canScheduleExactAlarms android.app.AlarmManager  canUseFullScreenIntent android.app.NotificationManager  Context android.content  Intent android.content  
ALARM_SERVICE android.content.Context  NOTIFICATION_SERVICE android.content.Context  checkSelfPermission android.content.Context  getSystemService android.content.Context  baseContext android.content.ContextWrapper  packageName android.content.ContextWrapper  
startActivity android.content.ContextWrapper  FLAG_ACTIVITY_NEW_TASK android.content.Intent  Intent android.content.Intent  Settings android.content.Intent  Uri android.content.Intent  addFlags android.content.Intent  apply android.content.Intent  putExtra android.content.Intent  	setAction android.content.Intent  setData android.content.Intent  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  Uri android.net  parse android.net.Uri  Build 
android.os  SDK_INT android.os.Build.VERSION  BASE android.os.Build.VERSION_CODES  O android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  UPSIDE_DOWN_CAKE android.os.Build.VERSION_CODES  Settings android.provider  #ACTION_APPLICATION_DETAILS_SETTINGS android.provider.Settings   ACTION_APP_NOTIFICATION_SETTINGS android.provider.Settings  (ACTION_MANAGE_APP_USE_FULL_SCREEN_INTENT android.provider.Settings  #ACTION_REQUEST_SCHEDULE_EXACT_ALARM android.provider.Settings  EXTRA_APP_PACKAGE android.provider.Settings  SparseArray android.util  get android.util.SparseArray  put android.util.SparseArray  remove android.util.SparseArray  size android.util.SparseArray  NotificationManagerCompat androidx.core.app  areNotificationsEnabled +androidx.core.app.NotificationManagerCompat  from +androidx.core.app.NotificationManagerCompat  FLog com.facebook.common.logging  e  com.facebook.common.logging.FLog  w  com.facebook.common.logging.FLog  TurboReactPackage com.facebook.react  BuildConfig #com.facebook.react.BaseReactPackage  HashMap #com.facebook.react.BaseReactPackage  
MutableMap #com.facebook.react.BaseReactPackage  NativeModule #com.facebook.react.BaseReactPackage  RNPermissionsModule #com.facebook.react.BaseReactPackage  RNPermissionsModuleImpl #com.facebook.react.BaseReactPackage  ReactApplicationContext #com.facebook.react.BaseReactPackage  ReactModuleInfo #com.facebook.react.BaseReactPackage  ReactModuleInfoProvider #com.facebook.react.BaseReactPackage  String #com.facebook.react.BaseReactPackage  set #com.facebook.react.BaseReactPackage  	Arguments com.facebook.react.bridge  Callback com.facebook.react.bridge  NativeModule com.facebook.react.bridge  Promise com.facebook.react.bridge  ReactApplicationContext com.facebook.react.bridge  
ReadableArray com.facebook.react.bridge  WritableMap com.facebook.react.bridge  WritableNativeMap com.facebook.react.bridge  	createMap #com.facebook.react.bridge.Arguments  Callback (com.facebook.react.bridge.BaseJavaModule  RNPermissionsModuleImpl (com.facebook.react.bridge.BaseJavaModule  SparseArray (com.facebook.react.bridge.BaseJavaModule  canScheduleExactAlarms (com.facebook.react.bridge.BaseJavaModule  canUseFullScreenIntent (com.facebook.react.bridge.BaseJavaModule  check (com.facebook.react.bridge.BaseJavaModule  checkLocationAccuracy (com.facebook.react.bridge.BaseJavaModule  
checkMultiple (com.facebook.react.bridge.BaseJavaModule  checkNotifications (com.facebook.react.bridge.BaseJavaModule  onRequestPermissionsResult (com.facebook.react.bridge.BaseJavaModule  openPhotoPicker (com.facebook.react.bridge.BaseJavaModule  openSettings (com.facebook.react.bridge.BaseJavaModule  reactApplicationContext (com.facebook.react.bridge.BaseJavaModule  request (com.facebook.react.bridge.BaseJavaModule  requestLocationAccuracy (com.facebook.react.bridge.BaseJavaModule  requestMultiple (com.facebook.react.bridge.BaseJavaModule  requestNotifications (com.facebook.react.bridge.BaseJavaModule  shouldShowRequestRationale (com.facebook.react.bridge.BaseJavaModule  invoke "com.facebook.react.bridge.Callback  reject !com.facebook.react.bridge.Promise  resolve !com.facebook.react.bridge.Promise  baseContext 1com.facebook.react.bridge.ReactApplicationContext  currentActivity 1com.facebook.react.bridge.ReactApplicationContext  getSystemService 1com.facebook.react.bridge.ReactApplicationContext  packageName 1com.facebook.react.bridge.ReactApplicationContext  
startActivity 1com.facebook.react.bridge.ReactApplicationContext  currentActivity &com.facebook.react.bridge.ReactContext  getSystemService &com.facebook.react.bridge.ReactContext  Callback 4com.facebook.react.bridge.ReactContextBaseJavaModule  RNPermissionsModuleImpl 4com.facebook.react.bridge.ReactContextBaseJavaModule  SparseArray 4com.facebook.react.bridge.ReactContextBaseJavaModule  canScheduleExactAlarms 4com.facebook.react.bridge.ReactContextBaseJavaModule  canUseFullScreenIntent 4com.facebook.react.bridge.ReactContextBaseJavaModule  check 4com.facebook.react.bridge.ReactContextBaseJavaModule  checkLocationAccuracy 4com.facebook.react.bridge.ReactContextBaseJavaModule  
checkMultiple 4com.facebook.react.bridge.ReactContextBaseJavaModule  checkNotifications 4com.facebook.react.bridge.ReactContextBaseJavaModule  onRequestPermissionsResult 4com.facebook.react.bridge.ReactContextBaseJavaModule  openPhotoPicker 4com.facebook.react.bridge.ReactContextBaseJavaModule  openSettings 4com.facebook.react.bridge.ReactContextBaseJavaModule  request 4com.facebook.react.bridge.ReactContextBaseJavaModule  requestLocationAccuracy 4com.facebook.react.bridge.ReactContextBaseJavaModule  requestMultiple 4com.facebook.react.bridge.ReactContextBaseJavaModule  requestNotifications 4com.facebook.react.bridge.ReactContextBaseJavaModule  shouldShowRequestRationale 4com.facebook.react.bridge.ReactContextBaseJavaModule  	getString 'com.facebook.react.bridge.ReadableArray  size 'com.facebook.react.bridge.ReadableArray  putMap %com.facebook.react.bridge.WritableMap  	putString %com.facebook.react.bridge.WritableMap  ReactModule %com.facebook.react.module.annotations  ReactModuleInfo com.facebook.react.module.model  ReactModuleInfoProvider com.facebook.react.module.model  PermissionAwareActivity com.facebook.react.modules.core  PermissionListener com.facebook.react.modules.core  requestPermissions 7com.facebook.react.modules.core.PermissionAwareActivity  $shouldShowRequestPermissionRationale 7com.facebook.react.modules.core.PermissionAwareActivity  AlarmManager com.zoontek.rnpermissions  	Arguments com.zoontek.rnpermissions  Array com.zoontek.rnpermissions  	ArrayList com.zoontek.rnpermissions  BLOCKED com.zoontek.rnpermissions  Boolean com.zoontek.rnpermissions  Build com.zoontek.rnpermissions  BuildConfig com.zoontek.rnpermissions  Callback com.zoontek.rnpermissions  Context com.zoontek.rnpermissions  DENIED com.zoontek.rnpermissions  	Exception com.zoontek.rnpermissions  FLog com.zoontek.rnpermissions  GRANTED com.zoontek.rnpermissions  HashMap com.zoontek.rnpermissions  IllegalStateException com.zoontek.rnpermissions  Int com.zoontek.rnpermissions  IntArray com.zoontek.rnpermissions  Intent com.zoontek.rnpermissions  
MutableMap com.zoontek.rnpermissions  NativeModule com.zoontek.rnpermissions  NativeRNPermissionsSpec com.zoontek.rnpermissions  NotificationManager com.zoontek.rnpermissions  NotificationManagerCompat com.zoontek.rnpermissions  PackageManager com.zoontek.rnpermissions  PermissionAwareActivity com.zoontek.rnpermissions  PermissionListener com.zoontek.rnpermissions  Promise com.zoontek.rnpermissions  RNPermissionsModule com.zoontek.rnpermissions  RNPermissionsModuleImpl com.zoontek.rnpermissions  RNPermissionsPackage com.zoontek.rnpermissions  ReactApplicationContext com.zoontek.rnpermissions  ReactModule com.zoontek.rnpermissions  ReactModuleInfo com.zoontek.rnpermissions  ReactModuleInfoProvider com.zoontek.rnpermissions  
ReadableArray com.zoontek.rnpermissions  Settings com.zoontek.rnpermissions  SparseArray com.zoontek.rnpermissions  String com.zoontek.rnpermissions  TurboReactPackage com.zoontek.rnpermissions  Uri com.zoontek.rnpermissions  WritableMap com.zoontek.rnpermissions  WritableNativeMap com.zoontek.rnpermissions  apply com.zoontek.rnpermissions  arrayOf com.zoontek.rnpermissions  canScheduleExactAlarms com.zoontek.rnpermissions  canUseFullScreenIntent com.zoontek.rnpermissions  check com.zoontek.rnpermissions  checkLocationAccuracy com.zoontek.rnpermissions  
checkMultiple com.zoontek.rnpermissions  checkNotNull com.zoontek.rnpermissions  checkNotifications com.zoontek.rnpermissions  forEachIndexed com.zoontek.rnpermissions  	getOrNull com.zoontek.rnpermissions  
isNullOrBlank com.zoontek.rnpermissions  mapOf com.zoontek.rnpermissions  onRequestPermissionsResult com.zoontek.rnpermissions  openPhotoPicker com.zoontek.rnpermissions  openSettings com.zoontek.rnpermissions  request com.zoontek.rnpermissions  requestLocationAccuracy com.zoontek.rnpermissions  requestMultiple com.zoontek.rnpermissions  requestNotifications com.zoontek.rnpermissions  set com.zoontek.rnpermissions  shouldShowRequestRationale com.zoontek.rnpermissions  to com.zoontek.rnpermissions  toTypedArray com.zoontek.rnpermissions  until com.zoontek.rnpermissions  IS_NEW_ARCHITECTURE_ENABLED %com.zoontek.rnpermissions.BuildConfig  Callback 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  RNPermissionsModuleImpl 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  SparseArray 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  canScheduleExactAlarms 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  canUseFullScreenIntent 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  check 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  checkLocationAccuracy 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  
checkMultiple 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  checkNotifications 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  onRequestPermissionsResult 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  openPhotoPicker 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  openSettings 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  request 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  requestLocationAccuracy 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  requestMultiple 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  requestNotifications 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  shouldShowRequestRationale 1com.zoontek.rnpermissions.NativeRNPermissionsSpec  RNPermissionsModuleImpl -com.zoontek.rnpermissions.RNPermissionsModule  SparseArray -com.zoontek.rnpermissions.RNPermissionsModule  	callbacks -com.zoontek.rnpermissions.RNPermissionsModule  canScheduleExactAlarms -com.zoontek.rnpermissions.RNPermissionsModule  canUseFullScreenIntent -com.zoontek.rnpermissions.RNPermissionsModule  check -com.zoontek.rnpermissions.RNPermissionsModule  checkLocationAccuracy -com.zoontek.rnpermissions.RNPermissionsModule  
checkMultiple -com.zoontek.rnpermissions.RNPermissionsModule  checkNotifications -com.zoontek.rnpermissions.RNPermissionsModule  onRequestPermissionsResult -com.zoontek.rnpermissions.RNPermissionsModule  openPhotoPicker -com.zoontek.rnpermissions.RNPermissionsModule  openSettings -com.zoontek.rnpermissions.RNPermissionsModule  reactApplicationContext -com.zoontek.rnpermissions.RNPermissionsModule  request -com.zoontek.rnpermissions.RNPermissionsModule  requestLocationAccuracy -com.zoontek.rnpermissions.RNPermissionsModule  requestMultiple -com.zoontek.rnpermissions.RNPermissionsModule  requestNotifications -com.zoontek.rnpermissions.RNPermissionsModule  shouldShowRequestRationale -com.zoontek.rnpermissions.RNPermissionsModule  	Arguments 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  	ArrayList 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  BLOCKED 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  Build 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  Callback 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  Context 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  DENIED 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  ERROR_INVALID_ACTIVITY 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  FLog 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  GRANTED 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  Intent 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  NAME 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  NotificationManagerCompat 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  PackageManager 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  Settings 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  UNAVAILABLE 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  Uri 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  WritableNativeMap 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  apply 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  arrayOf 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  canScheduleExactAlarms 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  canUseFullScreenIntent 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  check 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  checkLocationAccuracy 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  
checkMultiple 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  checkNotNull 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  checkNotifications 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  forEachIndexed 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  	getOrNull 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  getPermissionAwareActivity 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  
isNullOrBlank 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  isPermissionAvailable 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  mapOf 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  
minimumApi 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  onRequestPermissionsResult 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  openPhotoPicker 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  openSettings 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  request 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  requestCode 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  requestLocationAccuracy 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  requestMultiple 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  requestNotifications 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  shouldShowRequestRationale 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  to 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  toTypedArray 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  until 1com.zoontek.rnpermissions.RNPermissionsModuleImpl  BuildConfig .com.zoontek.rnpermissions.RNPermissionsPackage  HashMap .com.zoontek.rnpermissions.RNPermissionsPackage  RNPermissionsModule .com.zoontek.rnpermissions.RNPermissionsPackage  RNPermissionsModuleImpl .com.zoontek.rnpermissions.RNPermissionsPackage  ReactModuleInfo .com.zoontek.rnpermissions.RNPermissionsPackage  ReactModuleInfoProvider .com.zoontek.rnpermissions.RNPermissionsPackage  set .com.zoontek.rnpermissions.RNPermissionsPackage  	Exception 	java.lang  IllegalStateException 	java.lang  	ArrayList 	java.util  HashMap 	java.util  add java.util.ArrayList  forEachIndexed java.util.ArrayList  toTypedArray java.util.ArrayList  Array kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  IntArray kotlin  Nothing kotlin  Pair kotlin  apply kotlin  arrayOf kotlin  check kotlin  checkNotNull kotlin  to kotlin  get kotlin.Array  not kotlin.Boolean  	compareTo 
kotlin.Int  inc 
kotlin.Int  	getOrNull kotlin.IntArray  
isNullOrBlank 
kotlin.String  to 
kotlin.String  IntIterator kotlin.collections  Map kotlin.collections  
MutableMap kotlin.collections  forEachIndexed kotlin.collections  	getOrNull kotlin.collections  mapOf kotlin.collections  set kotlin.collections  toTypedArray kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.Map  set kotlin.collections.MutableMap  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  	getOrNull kotlin.text  
isNullOrBlank kotlin.text  set kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             