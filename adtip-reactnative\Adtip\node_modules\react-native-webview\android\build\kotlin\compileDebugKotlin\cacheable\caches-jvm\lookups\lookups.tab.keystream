  Activity android.app  DownloadManager android.app  requestedOrientation android.app.Activity  window android.app.Activity  Request android.app.DownloadManager  #VISIBILITY_VISIBLE_NOTIFY_COMPLETED #android.app.DownloadManager.Request  addRequestHeader #android.app.DownloadManager.Request  allowScanningByMediaScanner #android.app.DownloadManager.Request  setDescription #android.app.DownloadManager.Request  !setDestinationInExternalPublicDir #android.app.DownloadManager.Request  setNotificationVisibility #android.app.DownloadManager.Request  setTitle #android.app.DownloadManager.Request  Context android.content  ActivityInfo android.content.pm  SCREEN_ORIENTATION_UNSPECIFIED android.content.pm.ActivityInfo  Bitmap android.graphics  Color android.graphics  Config android.graphics.Bitmap  createBitmap android.graphics.Bitmap  	ARGB_8888 android.graphics.Bitmap.Config  BLACK android.graphics.Color  TRANSPARENT android.graphics.Color  Uri android.net  parse android.net.Uri  Build 
android.os  Environment 
android.os  SDK_INT android.os.Build.VERSION  O android.os.Build.VERSION_CODES  P android.os.Build.VERSION_CODES  DIRECTORY_DOWNLOADS android.os.Environment  Log android.util  w android.util.Log  View android.view  	ViewGroup android.view  
WindowManager android.view  Color android.view.View  GONE android.view.View  LAYER_TYPE_HARDWARE android.view.View  LAYER_TYPE_NONE android.view.View  LAYER_TYPE_SOFTWARE android.view.View  OVER_SCROLL_ALWAYS android.view.View  OVER_SCROLL_IF_CONTENT_SCROLLS android.view.View  OVER_SCROLL_NEVER android.view.View  
RNCWebView android.view.View  VISIBLE android.view.View  View android.view.View  context android.view.View  id android.view.View  isHorizontalScrollBarEnabled android.view.View  isVerticalScrollBarEnabled android.view.View  layoutParams android.view.View  overScrollMode android.view.View  parent android.view.View  requestFocus android.view.View  rootView android.view.View  setBackgroundColor android.view.View  systemUiVisibility android.view.View  
visibility android.view.View  Color android.view.ViewGroup  LayoutParams android.view.ViewGroup  
RNCWebView android.view.ViewGroup  View android.view.ViewGroup  addView android.view.ViewGroup  
getChildAt android.view.ViewGroup  
removeView android.view.ViewGroup  MATCH_PARENT #android.view.ViewGroup.LayoutParams  
clearFlags android.view.Window  setFlags android.view.Window  FLAG_LAYOUT_NO_LIMITS 'android.view.WindowManager.LayoutParams  
CookieManager android.webkit  DownloadListener android.webkit  WebChromeClient android.webkit  WebSettings android.webkit  WebView android.webkit  	getCookie android.webkit.CookieManager  getInstance android.webkit.CookieManager  removeAllCookies android.webkit.CookieManager  setAcceptThirdPartyCookies android.webkit.CookieManager  ActivityInfo android.webkit.WebChromeClient  Bitmap android.webkit.WebChromeClient  Color android.webkit.WebChromeClient  CustomViewCallback android.webkit.WebChromeClient  FULLSCREEN_LAYOUT_PARAMS android.webkit.WebChromeClient  FULLSCREEN_SYSTEM_UI_VISIBILITY android.webkit.WebChromeClient  View android.webkit.WebChromeClient  
WindowManager android.webkit.WebChromeClient  onHideCustomView android.webkit.WebChromeClient  onCustomViewHidden 1android.webkit.WebChromeClient.CustomViewCallback  LOAD_CACHE_ELSE_NETWORK android.webkit.WebSettings  LOAD_CACHE_ONLY android.webkit.WebSettings  LOAD_DEFAULT android.webkit.WebSettings  
LOAD_NO_CACHE android.webkit.WebSettings  MIXED_CONTENT_ALWAYS_ALLOW android.webkit.WebSettings   MIXED_CONTENT_COMPATIBILITY_MODE android.webkit.WebSettings  MIXED_CONTENT_NEVER_ALLOW android.webkit.WebSettings  allowContentAccess android.webkit.WebSettings  allowFileAccess android.webkit.WebSettings  allowFileAccessFromFileURLs android.webkit.WebSettings   allowUniversalAccessFromFileURLs android.webkit.WebSettings  builtInZoomControls android.webkit.WebSettings  	cacheMode android.webkit.WebSettings  displayZoomControls android.webkit.WebSettings  domStorageEnabled android.webkit.WebSettings  getDefaultUserAgent android.webkit.WebSettings  %javaScriptCanOpenWindowsAutomatically android.webkit.WebSettings  javaScriptEnabled android.webkit.WebSettings  loadWithOverviewMode android.webkit.WebSettings   mediaPlaybackRequiresUserGesture android.webkit.WebSettings  minimumFontSize android.webkit.WebSettings  mixedContentMode android.webkit.WebSettings  saveFormData android.webkit.WebSettings  savePassword android.webkit.WebSettings  setGeolocationEnabled android.webkit.WebSettings  setSupportMultipleWindows android.webkit.WebSettings  textZoom android.webkit.WebSettings  useWideViewPort android.webkit.WebSettings  userAgentString android.webkit.WebSettings  
clearCache android.webkit.WebView  
clearFormData android.webkit.WebView  clearHistory android.webkit.WebView  goBack android.webkit.WebView  	goForward android.webkit.WebView  loadDataWithBaseURL android.webkit.WebView  loadUrl android.webkit.WebView  parent android.webkit.WebView  postUrl android.webkit.WebView  reload android.webkit.WebView  requestFocus android.webkit.WebView  setBackgroundColor android.webkit.WebView  setDownloadListener android.webkit.WebView  setLayerType android.webkit.WebView  setWebContentsDebuggingEnabled android.webkit.WebView  settings android.webkit.WebView  stopLoading android.webkit.WebView  url android.webkit.WebView  FrameLayout android.widget  Color android.widget.FrameLayout  
RNCWebView android.widget.FrameLayout  View android.widget.FrameLayout  WebSettingsCompat androidx.webkit  WebViewFeature androidx.webkit  8DARK_STRATEGY_PREFER_WEB_THEME_OVER_USER_AGENT_DARKENING !androidx.webkit.WebSettingsCompat  FORCE_DARK_OFF !androidx.webkit.WebSettingsCompat  
FORCE_DARK_ON !androidx.webkit.WebSettingsCompat  setForceDark !androidx.webkit.WebSettingsCompat  setForceDarkStrategy !androidx.webkit.WebSettingsCompat  
FORCE_DARK androidx.webkit.WebViewFeature  FORCE_DARK_STRATEGY androidx.webkit.WebViewFeature  isFeatureSupported androidx.webkit.WebViewFeature  JavaScriptModule com.facebook.react.bridge  NativeModule com.facebook.react.bridge  
ReadableArray com.facebook.react.bridge  ReadableMap com.facebook.react.bridge  ReadableMapKeySetIterator com.facebook.react.bridge  WritableMap com.facebook.react.bridge  getNativeModule &com.facebook.react.bridge.ReactContext  
getBoolean 'com.facebook.react.bridge.ReadableArray  	getString 'com.facebook.react.bridge.ReadableArray  toArrayList 'com.facebook.react.bridge.ReadableArray  getArray %com.facebook.react.bridge.ReadableMap  getMap %com.facebook.react.bridge.ReadableMap  	getString %com.facebook.react.bridge.ReadableMap  hasKey %com.facebook.react.bridge.ReadableMap  keySetIterator %com.facebook.react.bridge.ReadableMap  let %com.facebook.react.bridge.ReadableMap  
hasNextKey 3com.facebook.react.bridge.ReadableMapKeySetIterator  nextKey 3com.facebook.react.bridge.ReadableMapKeySetIterator  
putBoolean %com.facebook.react.bridge.WritableMap  	putString %com.facebook.react.bridge.WritableMap  
MapBuilder com.facebook.react.common  Builder $com.facebook.react.common.MapBuilder  builder $com.facebook.react.common.MapBuilder  build ,com.facebook.react.common.MapBuilder.Builder  put ,com.facebook.react.common.MapBuilder.Builder  ReactBuildConfig com.facebook.react.common.build  DEBUG 0com.facebook.react.common.build.ReactBuildConfig  ThemedReactContext com.facebook.react.uimanager  addLifecycleEventListener /com.facebook.react.uimanager.ThemedReactContext  currentActivity /com.facebook.react.uimanager.ThemedReactContext  removeLifecycleEventListener /com.facebook.react.uimanager.ThemedReactContext  Event #com.facebook.react.uimanager.events  RCTEventEmitter #com.facebook.react.uimanager.events  
EVENT_NAME )com.facebook.react.uimanager.events.Event  viewTag )com.facebook.react.uimanager.events.Event  receiveEvent 3com.facebook.react.uimanager.events.RCTEventEmitter  ActivityInfo  com.reactnativecommunity.webview  Bitmap  com.reactnativecommunity.webview  Boolean  com.reactnativecommunity.webview  Build  com.reactnativecommunity.webview  	ByteArray  com.reactnativecommunity.webview  Color  com.reactnativecommunity.webview  Context  com.reactnativecommunity.webview  
CookieManager  com.reactnativecommunity.webview  CustomViewCallback  com.reactnativecommunity.webview  DownloadListener  com.reactnativecommunity.webview  DownloadManager  com.reactnativecommunity.webview  Environment  com.reactnativecommunity.webview  FULLSCREEN_LAYOUT_PARAMS  com.reactnativecommunity.webview  FULLSCREEN_SYSTEM_UI_VISIBILITY  com.reactnativecommunity.webview  FrameLayout  com.reactnativecommunity.webview  HashMap  com.reactnativecommunity.webview  IllegalArgumentException  com.reactnativecommunity.webview  Int  com.reactnativecommunity.webview  
JSONException  com.reactnativecommunity.webview  
JSONObject  com.reactnativecommunity.webview  JavaScriptModule  com.reactnativecommunity.webview  	JvmStatic  com.reactnativecommunity.webview  List  com.reactnativecommunity.webview  Locale  com.reactnativecommunity.webview  Log  com.reactnativecommunity.webview  MalformedURLException  com.reactnativecommunity.webview  Map  com.reactnativecommunity.webview  
MapBuilder  com.reactnativecommunity.webview  RNCBasicAuthCredential  com.reactnativecommunity.webview  RNCWebChromeClient  com.reactnativecommunity.webview  
RNCWebView  com.reactnativecommunity.webview  RNCWebViewConfig  com.reactnativecommunity.webview  RNCWebViewManagerImpl  com.reactnativecommunity.webview  RNCWebViewMessagingModule  com.reactnativecommunity.webview  RNCWebViewModule  com.reactnativecommunity.webview  RNCWebViewWrapper  com.reactnativecommunity.webview  ReactBuildConfig  com.reactnativecommunity.webview  
ReadableArray  com.reactnativecommunity.webview  ReadableMap  com.reactnativecommunity.webview  RuntimeException  com.reactnativecommunity.webview  String  com.reactnativecommunity.webview  ThemedReactContext  com.reactnativecommunity.webview  URL  com.reactnativecommunity.webview  URLUtil  com.reactnativecommunity.webview  UnsupportedEncodingException  com.reactnativecommunity.webview  Uri  com.reactnativecommunity.webview  View  com.reactnativecommunity.webview  	ViewGroup  com.reactnativecommunity.webview  WebSettings  com.reactnativecommunity.webview  WebSettingsCompat  com.reactnativecommunity.webview  WebView  com.reactnativecommunity.webview  WebViewFeature  com.reactnativecommunity.webview  
WindowManager  com.reactnativecommunity.webview  WritableMap  com.reactnativecommunity.webview  charset  com.reactnativecommunity.webview  equals  com.reactnativecommunity.webview  invalidCharRegex  com.reactnativecommunity.webview  java  com.reactnativecommunity.webview  let  com.reactnativecommunity.webview  	lowercase  com.reactnativecommunity.webview  replace  com.reactnativecommunity.webview  set  com.reactnativecommunity.webview  toByteArray  com.reactnativecommunity.webview  toRegex  com.reactnativecommunity.webview  Request 0com.reactnativecommunity.webview.DownloadManager  ActivityInfo 3com.reactnativecommunity.webview.RNCWebChromeClient  Bitmap 3com.reactnativecommunity.webview.RNCWebChromeClient  Color 3com.reactnativecommunity.webview.RNCWebChromeClient  FULLSCREEN_LAYOUT_PARAMS 3com.reactnativecommunity.webview.RNCWebChromeClient  FULLSCREEN_SYSTEM_UI_VISIBILITY 3com.reactnativecommunity.webview.RNCWebChromeClient  View 3com.reactnativecommunity.webview.RNCWebChromeClient  
WindowManager 3com.reactnativecommunity.webview.RNCWebChromeClient  mCustomViewCallback 3com.reactnativecommunity.webview.RNCWebChromeClient  
mVideoView 3com.reactnativecommunity.webview.RNCWebChromeClient  mWebView 3com.reactnativecommunity.webview.RNCWebChromeClient  onHideCustomView 3com.reactnativecommunity.webview.RNCWebChromeClient  rootView 3com.reactnativecommunity.webview.RNCWebChromeClient  setAllowsProtectedMedia 3com.reactnativecommunity.webview.RNCWebChromeClient  setHasOnOpenWindowEvent 3com.reactnativecommunity.webview.RNCWebChromeClient  cleanupCallbacksAndDestroy +com.reactnativecommunity.webview.RNCWebView  
clearCache +com.reactnativecommunity.webview.RNCWebView  
clearFormData +com.reactnativecommunity.webview.RNCWebView  clearHistory +com.reactnativecommunity.webview.RNCWebView  context +com.reactnativecommunity.webview.RNCWebView  evaluateJavascriptWithFallback +com.reactnativecommunity.webview.RNCWebView  goBack +com.reactnativecommunity.webview.RNCWebView  	goForward +com.reactnativecommunity.webview.RNCWebView  
injectedJS +com.reactnativecommunity.webview.RNCWebView  injectedJSBeforeContentLoaded +com.reactnativecommunity.webview.RNCWebView  5injectedJavaScriptBeforeContentLoadedForMainFrameOnly +com.reactnativecommunity.webview.RNCWebView  "injectedJavaScriptForMainFrameOnly +com.reactnativecommunity.webview.RNCWebView  isHorizontalScrollBarEnabled +com.reactnativecommunity.webview.RNCWebView  isVerticalScrollBarEnabled +com.reactnativecommunity.webview.RNCWebView  layoutParams +com.reactnativecommunity.webview.RNCWebView  loadDataWithBaseURL +com.reactnativecommunity.webview.RNCWebView  loadUrl +com.reactnativecommunity.webview.RNCWebView  mWebChromeClient +com.reactnativecommunity.webview.RNCWebView  messagingModuleName +com.reactnativecommunity.webview.RNCWebView  nestedScrollEnabled +com.reactnativecommunity.webview.RNCWebView  overScrollMode +com.reactnativecommunity.webview.RNCWebView  postUrl +com.reactnativecommunity.webview.RNCWebView  progressChangedFilter +com.reactnativecommunity.webview.RNCWebView  reactApplicationContext +com.reactnativecommunity.webview.RNCWebView  reload +com.reactnativecommunity.webview.RNCWebView  requestFocus +com.reactnativecommunity.webview.RNCWebView  setBackgroundColor +com.reactnativecommunity.webview.RNCWebView  setBasicAuthCredential +com.reactnativecommunity.webview.RNCWebView  setDownloadListener +com.reactnativecommunity.webview.RNCWebView  setHasScrollEvent +com.reactnativecommunity.webview.RNCWebView  setIgnoreErrFailedForThisURL +com.reactnativecommunity.webview.RNCWebView  setInjectedJavaScriptObject +com.reactnativecommunity.webview.RNCWebView  setLayerType +com.reactnativecommunity.webview.RNCWebView  setMenuCustomItems +com.reactnativecommunity.webview.RNCWebView  setMessagingEnabled +com.reactnativecommunity.webview.RNCWebView  setWebContentsDebuggingEnabled +com.reactnativecommunity.webview.RNCWebView  settings +com.reactnativecommunity.webview.RNCWebView  stopLoading +com.reactnativecommunity.webview.RNCWebView  themedReactContext +com.reactnativecommunity.webview.RNCWebView  url +com.reactnativecommunity.webview.RNCWebView  webChromeClient +com.reactnativecommunity.webview.RNCWebView  setWaitingForCommandLoadUrl Acom.reactnativecommunity.webview.RNCWebView.ProgressChangedFilter  
configWebView 1com.reactnativecommunity.webview.RNCWebViewConfig  ActivityInfo 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  	BLANK_URL 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Bitmap 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Boolean 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Build 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  	ByteArray 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_CLEAR_CACHE 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_CLEAR_FORM_DATA 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_CLEAR_HISTORY 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
COMMAND_FOCUS 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_GO_BACK 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_GO_FORWARD 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_INJECT_JAVASCRIPT 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_LOAD_URL 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_POST_MESSAGE 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_RELOAD 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  COMMAND_STOP_LOADING 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Color 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
CookieManager 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  CustomViewCallback 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  DEFAULT_DOWNLOADING_MESSAGE 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  +DEFAULT_LACK_PERMISSION_TO_DOWNLOAD_MESSAGE 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  DownloadListener 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  DownloadManager 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Environment 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  FULLSCREEN_LAYOUT_PARAMS 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  FULLSCREEN_SYSTEM_UI_VISIBILITY 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
HTML_ENCODING 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  HTML_MIME_TYPE 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  HTTP_METHOD_POST 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  HashMap 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  IllegalArgumentException 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Int 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
JSONException 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
JSONObject 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  List 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Locale 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Log 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  MalformedURLException 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Map 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
MapBuilder 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  RNCBasicAuthCredential 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  RNCWebChromeClient 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
RNCWebView 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  RNCWebViewConfig 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  RNCWebViewModule 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  RNCWebViewWrapper 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  ReactBuildConfig 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
ReadableArray 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  ReadableMap 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  RuntimeException 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  String 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  TAG 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  ThemedReactContext 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  URL 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  URLUtil 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  UnsupportedEncodingException 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  Uri 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  View 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  	ViewGroup 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  WebSettings 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  WebSettingsCompat 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  WebView 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  WebViewFeature 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
WindowManager 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  charset 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  createRNCWebViewInstance 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  createViewInstance 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  equals 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  getDownloadingMessageOrDefault 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  +getLackPermissionToDownloadMessageOrDefault 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  invalidCharRegex 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  java 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  let 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
loadSource 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  	lowercase 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  mAllowsFullscreenVideo 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  mAllowsProtectedMedia 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  mDownloadingMessage 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  mHasOnOpenWindowEvent 6com.reactnativecommunity.webview.RNCWebViewManagerImpl   mLackPermissionToDownloadMessage 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  mPendingSource 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  
mUserAgent 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  mUserAgentWithApplicationName 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  mWebViewConfig 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  newArch 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  replace 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  set 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  setUserAgentString 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  setupWebChromeClient 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  toByteArray 6com.reactnativecommunity.webview.RNCWebViewManagerImpl  ActivityInfo @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Bitmap @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Build @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  	ByteArray @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Color @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  
CookieManager @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  DownloadListener @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  DownloadManager @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Environment @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  FULLSCREEN_LAYOUT_PARAMS @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  FULLSCREEN_SYSTEM_UI_VISIBILITY @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  HashMap @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  
JSONObject @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Locale @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Log @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  
MapBuilder @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  RNCBasicAuthCredential @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  
RNCWebView @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  RNCWebViewConfig @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  RNCWebViewModule @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  RNCWebViewWrapper @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  ReactBuildConfig @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  RuntimeException @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  URL @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  URLUtil @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Uri @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  View @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  	ViewGroup @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  WebSettings @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  WebSettingsCompat @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  WebView @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  WebViewFeature @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  
WindowManager @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  charset @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  equals @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  invalidCharRegex @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  java @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  let @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  	lowercase @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  replace @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  set @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  toByteArray @com.reactnativecommunity.webview.RNCWebViewManagerImpl.Companion  Request Fcom.reactnativecommunity.webview.RNCWebViewManagerImpl.DownloadManager  downloadFile 1com.reactnativecommunity.webview.RNCWebViewModule  grantFileDownloaderPermissions 1com.reactnativecommunity.webview.RNCWebViewModule  setDownloadRequest 1com.reactnativecommunity.webview.RNCWebViewModule  Color 2com.reactnativecommunity.webview.RNCWebViewWrapper  Context 2com.reactnativecommunity.webview.RNCWebViewWrapper  Int 2com.reactnativecommunity.webview.RNCWebViewWrapper  	JvmStatic 2com.reactnativecommunity.webview.RNCWebViewWrapper  
RNCWebView 2com.reactnativecommunity.webview.RNCWebViewWrapper  View 2com.reactnativecommunity.webview.RNCWebViewWrapper  WebView 2com.reactnativecommunity.webview.RNCWebViewWrapper  addView 2com.reactnativecommunity.webview.RNCWebViewWrapper  
getChildAt 2com.reactnativecommunity.webview.RNCWebViewWrapper  webView 2com.reactnativecommunity.webview.RNCWebViewWrapper  Color <com.reactnativecommunity.webview.RNCWebViewWrapper.Companion  
guessFileName (com.reactnativecommunity.webview.URLUtil  Boolean 'com.reactnativecommunity.webview.events  
EVENT_NAME 'com.reactnativecommunity.webview.events  Event 'com.reactnativecommunity.webview.events  Int 'com.reactnativecommunity.webview.events  RCTEventEmitter 'com.reactnativecommunity.webview.events  Short 'com.reactnativecommunity.webview.events  String 'com.reactnativecommunity.webview.events  TopCustomMenuSelectionEvent 'com.reactnativecommunity.webview.events  TopHttpErrorEvent 'com.reactnativecommunity.webview.events  TopLoadingErrorEvent 'com.reactnativecommunity.webview.events  TopLoadingFinishEvent 'com.reactnativecommunity.webview.events  TopLoadingProgressEvent 'com.reactnativecommunity.webview.events  TopLoadingStartEvent 'com.reactnativecommunity.webview.events  TopMessageEvent 'com.reactnativecommunity.webview.events  TopOpenWindowEvent 'com.reactnativecommunity.webview.events  TopRenderProcessGoneEvent 'com.reactnativecommunity.webview.events  "TopShouldStartLoadWithRequestEvent 'com.reactnativecommunity.webview.events  WritableMap 'com.reactnativecommunity.webview.events  Boolean Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  
EVENT_NAME Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  Int Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  RCTEventEmitter Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  Short Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  String Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  WritableMap Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  	eventName Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  
mEventData Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  viewTag Ccom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent  
EVENT_NAME Mcom.reactnativecommunity.webview.events.TopCustomMenuSelectionEvent.Companion  Boolean 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  
EVENT_NAME 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  Int 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  RCTEventEmitter 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  Short 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  String 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  WritableMap 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  	eventName 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  
mEventData 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  viewTag 9com.reactnativecommunity.webview.events.TopHttpErrorEvent  
EVENT_NAME Ccom.reactnativecommunity.webview.events.TopHttpErrorEvent.Companion  Boolean <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  
EVENT_NAME <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  Int <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  RCTEventEmitter <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  Short <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  String <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  WritableMap <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  	eventName <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  
mEventData <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  viewTag <com.reactnativecommunity.webview.events.TopLoadingErrorEvent  
EVENT_NAME Fcom.reactnativecommunity.webview.events.TopLoadingErrorEvent.Companion  Boolean =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  
EVENT_NAME =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  Int =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  RCTEventEmitter =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  Short =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  String =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  WritableMap =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  	eventName =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  
mEventData =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  viewTag =com.reactnativecommunity.webview.events.TopLoadingFinishEvent  
EVENT_NAME Gcom.reactnativecommunity.webview.events.TopLoadingFinishEvent.Companion  Boolean ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  
EVENT_NAME ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  Int ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  RCTEventEmitter ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  Short ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  String ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  WritableMap ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  	eventName ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  
mEventData ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  viewTag ?com.reactnativecommunity.webview.events.TopLoadingProgressEvent  
EVENT_NAME Icom.reactnativecommunity.webview.events.TopLoadingProgressEvent.Companion  Boolean <com.reactnativecommunity.webview.events.TopLoadingStartEvent  
EVENT_NAME <com.reactnativecommunity.webview.events.TopLoadingStartEvent  Int <com.reactnativecommunity.webview.events.TopLoadingStartEvent  RCTEventEmitter <com.reactnativecommunity.webview.events.TopLoadingStartEvent  Short <com.reactnativecommunity.webview.events.TopLoadingStartEvent  String <com.reactnativecommunity.webview.events.TopLoadingStartEvent  WritableMap <com.reactnativecommunity.webview.events.TopLoadingStartEvent  	eventName <com.reactnativecommunity.webview.events.TopLoadingStartEvent  
mEventData <com.reactnativecommunity.webview.events.TopLoadingStartEvent  viewTag <com.reactnativecommunity.webview.events.TopLoadingStartEvent  
EVENT_NAME Fcom.reactnativecommunity.webview.events.TopLoadingStartEvent.Companion  Boolean 7com.reactnativecommunity.webview.events.TopMessageEvent  
EVENT_NAME 7com.reactnativecommunity.webview.events.TopMessageEvent  Int 7com.reactnativecommunity.webview.events.TopMessageEvent  RCTEventEmitter 7com.reactnativecommunity.webview.events.TopMessageEvent  Short 7com.reactnativecommunity.webview.events.TopMessageEvent  String 7com.reactnativecommunity.webview.events.TopMessageEvent  WritableMap 7com.reactnativecommunity.webview.events.TopMessageEvent  
mEventData 7com.reactnativecommunity.webview.events.TopMessageEvent  viewTag 7com.reactnativecommunity.webview.events.TopMessageEvent  
EVENT_NAME Acom.reactnativecommunity.webview.events.TopMessageEvent.Companion  Boolean :com.reactnativecommunity.webview.events.TopOpenWindowEvent  
EVENT_NAME :com.reactnativecommunity.webview.events.TopOpenWindowEvent  Int :com.reactnativecommunity.webview.events.TopOpenWindowEvent  RCTEventEmitter :com.reactnativecommunity.webview.events.TopOpenWindowEvent  Short :com.reactnativecommunity.webview.events.TopOpenWindowEvent  String :com.reactnativecommunity.webview.events.TopOpenWindowEvent  WritableMap :com.reactnativecommunity.webview.events.TopOpenWindowEvent  	eventName :com.reactnativecommunity.webview.events.TopOpenWindowEvent  
mEventData :com.reactnativecommunity.webview.events.TopOpenWindowEvent  viewTag :com.reactnativecommunity.webview.events.TopOpenWindowEvent  
EVENT_NAME Dcom.reactnativecommunity.webview.events.TopOpenWindowEvent.Companion  Boolean Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  
EVENT_NAME Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  Int Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  RCTEventEmitter Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  Short Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  String Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  WritableMap Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  	eventName Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  
mEventData Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  viewTag Acom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent  
EVENT_NAME Kcom.reactnativecommunity.webview.events.TopRenderProcessGoneEvent.Companion  Boolean Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  
EVENT_NAME Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  Int Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  RCTEventEmitter Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  Short Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  String Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  WritableMap Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  mData Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  viewTag Jcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent  
EVENT_NAME Tcom.reactnativecommunity.webview.events.TopShouldStartLoadWithRequestEvent.Companion  UnsupportedEncodingException java.io  Class 	java.lang  IllegalArgumentException 	java.lang  RuntimeException 	java.lang  MalformedURLException java.net  URL java.net  host java.net.URL  protocol java.net.URL  Charset java.nio.charset  	ArrayList 	java.util  HashMap 	java.util  Locale 	java.util  iterator java.util.ArrayList  get java.util.HashMap  set java.util.HashMap  ENGLISH java.util.Locale  	ByteArray kotlin  	Function1 kotlin  	Function5 kotlin  Nothing kotlin  let kotlin  equals 
kotlin.Any  not kotlin.Boolean  	compareTo 
kotlin.Int  equals 
kotlin.String  	lowercase 
kotlin.String  plus 
kotlin.String  toByteArray 
kotlin.String  toRegex 
kotlin.String  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  set kotlin.collections  toByteArray kotlin.collections  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  	JvmStatic 
kotlin.jvm  java 
kotlin.jvm  java kotlin.reflect.KClass  Regex kotlin.text  charset kotlin.text  equals kotlin.text  	lowercase kotlin.text  replace kotlin.text  set kotlin.text  toByteArray kotlin.text  toRegex kotlin.text  
JSONException org.json  
JSONObject org.json  put org.json.JSONObject  toString org.json.JSONObject                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     