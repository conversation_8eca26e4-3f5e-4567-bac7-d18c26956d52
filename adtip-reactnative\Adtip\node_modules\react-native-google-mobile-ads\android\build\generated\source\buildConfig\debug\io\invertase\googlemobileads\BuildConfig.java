/**
 * Automatically generated file. DO NOT MODIFY
 */
package io.invertase.googlemobileads;

public final class BuildConfig {
  public static final boolean DEBUG = Boolean.parseBoolean("true");
  public static final String LIBRARY_PACKAGE_NAME = "io.invertase.googlemobileads";
  public static final String BUILD_TYPE = "debug";
  // Field from default config.
  public static final String GOOGLE_MOBILE_ADS_JSON_RAW = "{\"android_app_id\":\"ca-app-pub-3206456546664189~6654042212\",\"ios_app_id\":\"ca-app-pub-3206456546664189~6654042212\"}";
  // Field from default config.
  public static final boolean IS_NEW_ARCHITECTURE_ENABLED = true;
}
