3com.mrousavy.camera.core.CameraConfiguration.Outputkotlin.Throwable$com.mrousavy.camera.core.CameraError&com.mrousavy.camera.core.RecorderErrorjava.io.Closeable!androidx.lifecycle.LifecycleOwner4com.mrousavy.camera.core.OrientationManager.Callback+androidx.camera.core.ImageAnalysis.Analyzer!android.location.LocationListener+com.mrousavy.camera.core.types.JSUnionValuekotlin.Enum5com.mrousavy.camera.core.types.JSUnionValue.Companion4com.facebook.react.bridge.ReactContextBaseJavaModulecom.facebook.react.ReactPackageandroid.widget.FrameLayout/com.mrousavy.camera.core.CameraSession.Callback5com.mrousavy.camera.react.FpsSampleCollector.Callback-com.facebook.react.uimanager.ViewGroupManager)com.facebook.react.uimanager.events.Event                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            