<com.mrousavy.camera.core.CameraConfiguration.Output.Disabled;com.mrousavy.camera.core.CameraConfiguration.Output.Enabled7com.mrousavy.camera.core.CameraConfiguration.AbortThrow$com.mrousavy.camera.core.CameraError.com.mrousavy.camera.core.CameraPermissionError2com.mrousavy.camera.core.MicrophonePermissionError0com.mrousavy.camera.core.LocationPermissionError4com.mrousavy.camera.core.InvalidTypeScriptUnionError,com.mrousavy.camera.core.NoCameraDeviceError5com.mrousavy.camera.core.PixelFormatNotSupportedError.com.mrousavy.camera.core.FlashUnavailableError/com.mrousavy.camera.core.FocusNotSupportedError)com.mrousavy.camera.core.CameraInUseError)com.mrousavy.camera.core.FatalCameraError,com.mrousavy.camera.core.CameraNotReadyError'com.mrousavy.camera.core.NoOutputsError)com.mrousavy.camera.core.RecoverableError8com.mrousavy.camera.core.InvalidOutputConfigurationError;com.mrousavy.camera.core.PropRequiresFormatToBeNonNullError(com.mrousavy.camera.core.InvalidFpsError6com.mrousavy.camera.core.InvalidVideoStabilizationMode-com.mrousavy.camera.core.InvalidVideoHdrErrorFcom.mrousavy.camera.core.PhotoHdrAndVideoHdrNotSupportedSimultaneously9com.mrousavy.camera.core.LowLightBoostNotSupportedWithHdr-com.mrousavy.camera.core.VideoNotEnabledError-com.mrousavy.camera.core.PhotoNotEnabledError,com.mrousavy.camera.core.SnapshotFailedError=com.mrousavy.camera.core.SnapshotFailedPreviewNotEnabledError+com.mrousavy.camera.core.FocusCanceledError2com.mrousavy.camera.core.FocusRequiresPreviewError&com.mrousavy.camera.core.RecorderError-com.mrousavy.camera.core.UnknownRecorderError%com.mrousavy.camera.core.EncoderError$com.mrousavy.camera.core.NoDataError:com.mrousavy.camera.core.InvalidRecorderConfigurationError2com.mrousavy.camera.core.FileSizeLimitReachedError2com.mrousavy.camera.core.DurationLimitReachedError<com.mrousavy.camera.core.InsufficientStorageForRecorderError3com.mrousavy.camera.core.NoRecordingInProgressError/com.mrousavy.camera.core.RecordingCanceledError$com.mrousavy.camera.core.FileIOError)com.mrousavy.camera.core.InvalidPathError1com.mrousavy.camera.core.RecordingInProgressError*com.mrousavy.camera.core.FrameInvalidError.com.mrousavy.camera.core.InvalidImageTypeError2com.mrousavy.camera.core.CodeTypeNotSupportedError*com.mrousavy.camera.core.ViewNotFoundError9com.mrousavy.camera.core.HardwareBuffersNotAvailableError-com.mrousavy.camera.core.MaxCamerasInUseError0com.mrousavy.camera.core.CameraIsRestrictedError-com.mrousavy.camera.core.DoNotDisturbBugErrorAcom.mrousavy.camera.core.RecordingWhileFrameProcessingUnavailable+com.mrousavy.camera.core.UnknownCameraError&com.mrousavy.camera.core.CameraSession,com.mrousavy.camera.core.CodeScannerPipeline/com.mrousavy.camera.core.FrameProcessorPipeline)com.mrousavy.camera.core.MetadataProvider.com.mrousavy.camera.core.types.AutoFocusSystem8com.mrousavy.camera.core.types.AutoFocusSystem.Companion'com.mrousavy.camera.core.types.CodeType1com.mrousavy.camera.core.types.CodeType.Companion)com.mrousavy.camera.core.types.DeviceType$com.mrousavy.camera.core.types.Flash.com.mrousavy.camera.core.types.Flash.Companion,com.mrousavy.camera.core.types.HardwareLevel*com.mrousavy.camera.core.types.Orientation4com.mrousavy.camera.core.types.Orientation.Companion0com.mrousavy.camera.core.types.OutputOrientation:com.mrousavy.camera.core.types.OutputOrientation.Companion/com.mrousavy.camera.core.types.PermissionStatus*com.mrousavy.camera.core.types.PixelFormat4com.mrousavy.camera.core.types.PixelFormat.Companion'com.mrousavy.camera.core.types.Position.com.mrousavy.camera.core.types.PreviewViewType8com.mrousavy.camera.core.types.PreviewViewType.Companion-com.mrousavy.camera.core.types.QualityBalance7com.mrousavy.camera.core.types.QualityBalance.Companion)com.mrousavy.camera.core.types.ResizeMode3com.mrousavy.camera.core.types.ResizeMode.Companion*com.mrousavy.camera.core.types.ShutterType$com.mrousavy.camera.core.types.Torch.com.mrousavy.camera.core.types.Torch.Companion)com.mrousavy.camera.core.types.VideoCodec3com.mrousavy.camera.core.types.VideoCodec.Companion,com.mrousavy.camera.core.types.VideoFileType6com.mrousavy.camera.core.types.VideoFileType.Companion5com.mrousavy.camera.core.types.VideoStabilizationMode?com.mrousavy.camera.core.types.VideoStabilizationMode.Companion.com.mrousavy.camera.react.CameraDevicesManager'com.mrousavy.camera.react.CameraPackage$com.mrousavy.camera.react.CameraView+com.mrousavy.camera.react.CameraViewManager*com.mrousavy.camera.react.CameraViewModule0com.mrousavy.camera.react.CameraInitializedEvent,com.mrousavy.camera.react.CameraStartedEvent,com.mrousavy.camera.react.CameraStoppedEvent3com.mrousavy.camera.react.CameraPreviewStartedEvent3com.mrousavy.camera.react.CameraPreviewStoppedEvent,com.mrousavy.camera.react.CameraShutterEvent=com.mrousavy.camera.react.CameraOutputOrientationChangedEvent>com.mrousavy.camera.react.CameraPreviewOrientationChangedEvent0com.mrousavy.camera.react.AverageFpsChangedEvent*com.mrousavy.camera.react.CameraErrorEvent.com.mrousavy.camera.react.CameraViewReadyEvent0com.mrousavy.camera.react.CameraCodeScannedEvent                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     